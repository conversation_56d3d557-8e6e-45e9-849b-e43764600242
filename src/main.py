"""
VuManChu Cipher B Main Application
Orchestrates data acquisition, indicator calculation, and visualization
"""

import numpy as np
import pandas as pd
import time
import json
import logging
from typing import Optional, Dict, Any
from datetime import datetime

from src.parameters import VuManChuParams, DEFAULT_PARAMS
from src.data_manager import DataManager
from src.indicators import generate_signals
from src.visualization import ChartVisualizer

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)


class VuManChuCipherB:
    """
    Main VuManChu Cipher B application class
    """
    
    def __init__(
        self, 
        params: Optional[VuManChuParams] = None,
        api_key: Optional[str] = None,
        api_secret: Optional[str] = None
    ):
        """
        Initialize VuManChu Cipher B application
        
        Args:
            params: VuManChu parameter set (uses DEFAULT_PARAMS if None)
            api_key: Binance API key (optional for public data)
            api_secret: Binance API secret (optional for public data)
        """
        self.params = params if params else DEFAULT_PARAMS
        self.data_manager = DataManager(api_key, api_secret)
        self.visualizer = ChartVisualizer(self.params)
        
        self.current_data = pd.DataFrame()
        self.current_indicators = {}
        self.is_running = False
        
        logger.info("VuManChu Cipher B initialized")
    
    def validate_parameters(self) -> bool:
        """
        Validate all parameters
        
        Returns:
            True if parameters are valid
        """
        try:
            return self.params.validate()
        except Exception as e:
            logger.error(f"Parameter validation failed: {e}")
            return False
    
    def load_historical_data(
        self, 
        symbol: str = 'BTCUSDT',
        interval: str = '4h',
        lookback_days: int = 100
    ) -> bool:
        """
        Load historical data for analysis
        
        Args:
            symbol: Trading pair symbol
            interval: Timeframe
            lookback_days: Number of days to look back
            
        Returns:
            True if data loaded successfully
        """
        try:
            logger.info(f"Loading historical data for {symbol} ({interval})")
            
            # Fetch historical data
            df = self.data_manager.get_historical_data(symbol, interval, lookback_days)
            
            if df.empty:
                logger.error("No historical data received")
                return False
            
            # Validate data
            if not self.data_manager.validate_data(df):
                logger.error("Historical data validation failed")
                return False
            
            self.current_data = df
            logger.info(f"Loaded {len(df)} historical candles")
            return True
            
        except Exception as e:
            logger.error(f"Error loading historical data: {e}")
            return False
    
    def calculate_indicators(self) -> bool:
        """
        Calculate all VuManChu Cipher B indicators
        
        Returns:
            True if calculation successful
        """
        try:
            if self.current_data.empty:
                logger.error("No data available for indicator calculation")
                return False
            
            logger.info("Calculating indicators...")
            start_time = time.time()
            
            # Extract OHLC data
            high = self.current_data['high'].values
            low = self.current_data['low'].values
            close = self.current_data['close'].values
            
            # Generate all signals and indicators
            self.current_indicators = generate_signals(high, low, close, self.params)
            
            calculation_time = time.time() - start_time
            logger.info(f"Indicators calculated in {calculation_time:.3f} seconds")
            
            return True
            
        except Exception as e:
            logger.error(f"Error calculating indicators: {e}")
            return False
    
    def get_latest_signals(self) -> Dict[str, Any]:
        """
        Get latest trading signals
        
        Returns:
            Dictionary with latest signal information
        """
        if not self.current_indicators:
            return {}
        
        # Get last index
        last_idx = len(self.current_data) - 1
        
        signals = {
            'timestamp': self.current_data.index[last_idx].isoformat(),
            'price': float(self.current_data.iloc[last_idx]['close']),
            'wt1': float(self.current_indicators['wt1'][last_idx]),
            'wt2': float(self.current_indicators['wt2'][last_idx]),
            'rsi': float(self.current_indicators['rsi'][last_idx]),
            'buy_signal': bool(self.current_indicators['buy_signals'][last_idx]),
            'sell_signal': bool(self.current_indicators['sell_signals'][last_idx]),
            'gold_signal': bool(self.current_indicators['gold_signals'][last_idx]),
            'bullish_divergence': bool(self.current_indicators['bullish_div'][last_idx]),
            'bearish_divergence': bool(self.current_indicators['bearish_div'][last_idx]),
            'sommi_flag_bullish': bool(self.current_indicators['sommi_flag_bullish'][last_idx]),
            'sommi_flag_bearish': bool(self.current_indicators['sommi_flag_bearish'][last_idx])
        }
        
        return signals
    
    def create_chart_data(self) -> Dict[str, Any]:
        """
        Create complete chart data for visualization
        
        Returns:
            Chart data structure
        """
        if self.current_data.empty or not self.current_indicators:
            return {}
        
        try:
            return self.visualizer.create_complete_chart_data(
                self.current_data, 
                self.current_indicators
            )
        except Exception as e:
            logger.error(f"Error creating chart data: {e}")
            return {}
    
    def print_latest_signals(self):
        """
        Print latest signals to console
        """
        signals = self.get_latest_signals()
        
        if not signals:
            logger.warning("No signals available")
            return
        
        print("\n" + "="*60)
        print("VUMANCHU CIPHER B - LATEST SIGNALS")
        print("="*60)
        print(f"Timestamp: {signals['timestamp']}")
        print(f"Price: ${signals['price']:.2f}")
        print(f"WT1: {signals['wt1']:.2f}")
        print(f"WT2: {signals['wt2']:.2f}")
        print(f"RSI: {signals['rsi']:.2f}")
        print("-"*60)
        
        # Trading signals
        if signals['buy_signal']:
            print("🟢 BUY SIGNAL DETECTED")
        if signals['sell_signal']:
            print("🔴 SELL SIGNAL DETECTED")
        if signals['gold_signal']:
            print("🟡 GOLD SIGNAL DETECTED (Strong Bullish)")
        
        # Divergences
        if signals['bullish_divergence']:
            print("📈 BULLISH DIVERGENCE")
        if signals['bearish_divergence']:
            print("📉 BEARISH DIVERGENCE")
        
        # Sommi patterns
        if signals['sommi_flag_bullish']:
            print("🚩 SOMMI BULLISH FLAG")
        if signals['sommi_flag_bearish']:
            print("🚩 SOMMI BEARISH FLAG")
        
        print("="*60)
    
    def run_analysis(
        self, 
        symbol: str = 'BTCUSDT',
        interval: str = '4h',
        lookback_days: int = 100
    ) -> bool:
        """
        Run complete analysis pipeline
        
        Args:
            symbol: Trading pair symbol
            interval: Timeframe
            lookback_days: Number of days to look back
            
        Returns:
            True if analysis completed successfully
        """
        try:
            logger.info("Starting VuManChu Cipher B analysis")
            
            # Validate parameters
            if not self.validate_parameters():
                logger.error("Parameter validation failed")
                return False
            
            # Load historical data
            if not self.load_historical_data(symbol, interval, lookback_days):
                logger.error("Failed to load historical data")
                return False
            
            # Calculate indicators
            if not self.calculate_indicators():
                logger.error("Failed to calculate indicators")
                return False
            
            # Print results
            self.print_latest_signals()
            
            logger.info("Analysis completed successfully")
            return True
            
        except Exception as e:
            logger.error(f"Error in analysis pipeline: {e}")
            return False
    
    def save_results(self, filename: str = 'vumanchu_results.json'):
        """
        Save analysis results to file
        
        Args:
            filename: Output filename
        """
        try:
            results = {
                'parameters': {
                    'wavetrend': {
                        'channel_len': self.params.wavetrend.wt_channel_len,
                        'average_len': self.params.wavetrend.wt_average_len,
                        'ma_len': self.params.wavetrend.wt_ma_len
                    }
                },
                'latest_signals': self.get_latest_signals(),
                'chart_data': self.create_chart_data()
            }
            
            with open(filename, 'w') as f:
                json.dump(results, f, indent=2, default=str)
            
            logger.info(f"Results saved to {filename}")
            
        except Exception as e:
            logger.error(f"Error saving results: {e}")


def main():
    """
    Main entry point
    """
    try:
        # Initialize application
        app = VuManChuCipherB()
        
        # Run analysis
        success = app.run_analysis(
            symbol='BTCUSDT',
            interval='4h',
            lookback_days=100
        )
        
        if success:
            # Save results
            app.save_results()
            logger.info("VuManChu Cipher B analysis completed successfully")
        else:
            logger.error("Analysis failed")
            
    except KeyboardInterrupt:
        logger.info("Analysis interrupted by user")
    except Exception as e:
        logger.error(f"Unexpected error: {e}")


if __name__ == "__main__":
    main()
